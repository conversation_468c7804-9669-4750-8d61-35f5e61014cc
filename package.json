{"name": "league-visualizer", "version": "1.0.0", "type": "commonjs", "description": "Shows leagues based on matches", "main": "index.ts", "scripts": {"build": "tsc", "start": "ts-node index.ts", "dev": "ts-node-dev --respawn index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<EMAIL>", "license": "ISC", "devDependencies": {"typescript": "^5.6.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "@types/node": "^20.10.0"}}