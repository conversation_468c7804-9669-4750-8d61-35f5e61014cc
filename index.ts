import fs from "fs";

const readMatchesFromFile = (filePath: string) => {
  const matches = JSON.parse(fs.readFileSync(filePath, "utf8"));
  return matches.data;
};

const main = () => {
  const matches = readMatchesFromFile("./matches.json");
  console.log(matches);
};

main();



type TeamResults = {
  teamName: string;
  points: number;
  wins: number;
  draws: number;
  losses: number;
  goalsScored: number;
  goalsAgainst: number;
  goalDifference: number;
}

type LeagueStanding = {
  round: number;
  standing: TeamResults[];
}

